"use client";

import { useEffect, useState, useMemo } from "react";
import Image from "next/image";
import { useParams, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { CampaignApplicationModal } from "@/components/ui/CampaignApplicationModal";
import { FullPageLoadingSpinner, LoadingSpinner } from "@/components/ui/LoadingSpinner";
import { Table, TableColumn } from "@/components/ui/Table";
import { useToast } from "@/components/ui/toast/use-toast";
import { trpc } from "@/lib/trpc/client";
import { client } from "@/lib/trpc/client";
import { ChevronDownIcon, UserPlusIcon, CheckIcon, XMarkIcon, DocumentArrowUpIcon, PlusIcon, GiftIcon } from "@heroicons/react/24/outline";
import { format } from "date-fns";
import Link from "next/link";
import { useAuth } from "@/hooks/use-auth";

import { Deliverable } from "@repo/server/src/models/deliverable";
import { ApplicationStatus } from "@repo/server/src/types/campaign";
import { DeliverableType, getDeliverableTypeLabel } from "@repo/server/src/types/deliverable";
import { ApplicationContractStatus } from "@/components/campaign/ApplicationContractStatus";
import { CampaignContractOverview } from "@/components/campaign/CampaignContractOverview";
import { DeliverableSubmissionModal } from "@/components/deliverable/DeliverableSubmissionModal";
import { DeliverableReviewModal } from "@/components/deliverable/DeliverableReviewModal";
import { SubmissionStatusBadge } from "@/components/deliverable/SubmissionStatusBadge";
import { SerializedDeliverableSubmission, DeliverableSubmissionStatus } from "@/types/deliverableSubmission";

interface Application {
  id: string;
  campaignId: string;
  athleteId:
    | {
        _id: string;
        userId: {
          _id: string;
          name: string;
        } | null;
        profilePicture?: {
          url: string;
        };
      }
    | string
    | null;
  status: ApplicationStatus;
  message?: string;
  deliverables?: string[] | { name: string }[];
  appliedAt: string;
  updatedAt: string;
  compensation?: number;
  initiatedBy: "brand" | "athlete";
}

export default function CampaignPage() {
  const [isApplicationModalOpen, setIsApplicationModalOpen] = useState(false);
  const [applications, setApplications] = useState<Application[]>([]);
  const [updatingId, setUpdatingId] = useState<string | null>(null);
  const [isSubmissionModalOpen, setIsSubmissionModalOpen] = useState(false);
  const [selectedDeliverable, setSelectedDeliverable] = useState<{ id: string; name: string } | null>(null);
  const [selectedSubmissionForResubmit, setSelectedSubmissionForResubmit] = useState<SerializedDeliverableSubmission | null>(null);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [selectedSubmission, setSelectedSubmission] = useState<SerializedDeliverableSubmission | null>(null);
  const { id } = useParams();
  const searchParams = useSearchParams();
  const hideApplicationButton = (searchParams.get("hideApply") as string) === "true";
  const { toast } = useToast();
  const { user } = useAuth();
  const brandView = (searchParams.get("brandView") as string) === "true" || user?.userType === "brand";

  // Initialize activeTab based on URL parameter, defaulting to "details"
  const initialTab = searchParams.get("tab") === "participant-management" ? "participant-management" : "details";
  const [activeTab, setActiveTab] = useState<"details" | "participant-management">(initialTab);
  const utils = trpc.useUtils();
  const { data: campaign, isLoading: isCampaignLoading } =
    trpc.campaign.getByCampaignId.useQuery({
      campaignId: id as string,
    });
  const { data: brand, isLoading: isBrandLoading } =
    trpc.brand.getBrand.useQuery(campaign?.brandId as string);
  const { data: athleteApplications, isLoading: isApplicationsLoading } =
    trpc.campaign.getAthleteApplications.useQuery(undefined, {
      enabled: !brandView,
    });

  // Check if athlete has active contract for this campaign
  const { data: hasActiveContract } = trpc.deliverableSubmission.hasActiveContract.useQuery(
    { campaignId: id as string },
    {
      enabled: !brandView && user?.userType === "athlete" && !!id,
      refetchOnWindowFocus: false,
    }
  );

  // Get athlete's submissions for this campaign
  const { data: athleteSubmissions, refetch: refetchSubmissions, isLoading: isSubmissionsLoading } =
    trpc.deliverableSubmission.getAthleteSubmissions.useQuery(
      { campaignId: id as string },
      {
        enabled: !brandView && user?.userType === "athlete" && !!id,
        refetchOnWindowFocus: false,
        staleTime: 0, // Always consider data stale to ensure fresh data
      }
    );

  // Get campaign submissions for brands
  const { data: campaignSubmissions, refetch: refetchCampaignSubmissions } =
    trpc.deliverableSubmission.getCampaignSubmissions.useQuery(
      { campaignId: id as string },
      {
        enabled: brandView && user?.userType === "brand" && !!id,
        refetchOnWindowFocus: false,
        staleTime: 0,
      }
    );

  const isLoading =
    isCampaignLoading || isBrandLoading || isApplicationsLoading;

  // Fetch campaign applications for brand view
  useEffect(() => {
    const fetchApplications = async () => {
      if (brandView && id) {
        try {
          const applicationsData = await client.campaign.getCampaignApplications.query({
            campaignId: id as string,
          });
          setApplications(applicationsData as Application[]);
        } catch (error) {
          toast({
            title: "Error",
            description: error instanceof Error ? error.message : "Failed to fetch applications",
            variant: "destructive",
          });
        }
      }
    };
    fetchApplications();
  }, [brandView, id, toast]);

  const applicationStatus = athleteApplications?.find(
    (app) => app.campaignId === id,
  )?.status;

  // Helper functions for deliverable submissions
  const handleSubmitDeliverable = (deliverableId: string, deliverableName: string, existingSubmission?: SerializedDeliverableSubmission) => {
    setSelectedDeliverable({ id: deliverableId, name: deliverableName });
    setSelectedSubmissionForResubmit(existingSubmission || null);
    setIsSubmissionModalOpen(true);
  };

  const handleSubmissionComplete = async (submission: SerializedDeliverableSubmission) => {
    // Refresh submissions data
    try {
      await refetchSubmissions();
      console.log("Submissions refetched successfully");
    } catch (error) {
      console.error("Error refetching submissions:", error);
    }

    toast({
      title: "Submission successful",
      description: `Your submission for "${submission.deliverable?.name}" has been submitted for review`,
    });
  };

  // Create a lookup map for submissions to avoid repeated array searches
  const submissionsByDeliverableId = useMemo(() => {
    if (!athleteSubmissions) return {};

    const map: Record<string, SerializedDeliverableSubmission> = {};
    athleteSubmissions.forEach(submission => {
      if (submission.deliverable?.id) {
        map[submission.deliverable.id] = submission;
      }
    });

    console.log("Submissions lookup map:", map);
    console.log("All athlete submissions:", athleteSubmissions);

    return map;
  }, [athleteSubmissions]);

  const getDeliverableSubmission = (deliverableId: string) => {
    return submissionsByDeliverableId[deliverableId];
  };

  // Handler functions for brand review
  const handleReviewSubmission = (submission: SerializedDeliverableSubmission) => {
    setSelectedSubmission(submission);
    setIsReviewModalOpen(true);
  };

  const handleReviewComplete = (updatedSubmission: SerializedDeliverableSubmission) => {
    // Refresh campaign submissions data
    refetchCampaignSubmissions();
    toast({
      title: "Review completed",
      description: `Submission has been ${updatedSubmission.status.toLowerCase().replace('_', ' ')}`,
    });
  };

  // Get submissions for a specific deliverable (brand view)
  const getDeliverableSubmissions = (deliverableId: string) => {
    return campaignSubmissions?.filter(sub => sub.deliverable?.id === deliverableId) || [];
  };
  if (isLoading) {
    return <FullPageLoadingSpinner />;
  }
  if (!campaign || !brand) {
    return (
      <div className="tw-flex tw-justify-center tw-items-center tw-min-h-[200px] tw-p-4">
        <div className="tw-text-aims-text-secondary tw-text-center">
          <div className="tw-text-lg tw-mb-2">Campaign not found</div>
          <div className="tw-text-sm">The campaign you&apos;re looking for doesn&apos;t exist or has been removed.</div>
        </div>
      </div>
    );
  }

  const handleApplicationArea = () => {
    if (brandView || hideApplicationButton) {
      return null;
    }
    const base = (applicationStatus: ApplicationStatus, color: string) => (
      <div
        className={`tw-mt-4 tw-text-aims-text-secondary tw-text-sm tw-text-center tw-border tw-border-aims-dark-3 tw-rounded-lg tw-p-3 sm:tw-p-2 tw-min-h-[44px] tw-flex tw-items-center tw-justify-center ${color}`}
      >
        Your application is {applicationStatus.toLowerCase()}.
      </div>
    );
    if (applicationStatus === ApplicationStatus.PENDING) {
      return base(ApplicationStatus.PENDING, "tw-bg-gray-500");
    } else if (applicationStatus === ApplicationStatus.ACCEPTED) {
      return base(ApplicationStatus.ACCEPTED, "tw-bg-green-500");
    } else if (applicationStatus === ApplicationStatus.REJECTED) {
      return base(ApplicationStatus.REJECTED, "tw-bg-red-500");
    } else if (applicationStatus === ApplicationStatus.WITHDRAWN) {
      return base(ApplicationStatus.WITHDRAWN, "tw-bg-gray-500");
    } else {
      return (
        <Button
          className="tw-mt-4 tw-w-full sm:tw-w-auto tw-h-12 sm:tw-h-10 tw-text-sm sm:tw-text-base tw-gap-2"
          onClick={() => setIsApplicationModalOpen(true)}
          disabled={brandView}
        >
          <UserPlusIcon className="tw-w-5 tw-h-5" />
          Apply to participate
        </Button>
      );
    }
  };

  const handleUpdateStatus = async (
    applicationId: string,
    status: ApplicationStatus,
  ) => {
    try {
      setUpdatingId(applicationId);
      await client.campaign.updateApplicationStatus.mutate({
        applicationId,
        status,
      });

      // Update local state
      setApplications((prev) =>
        prev.map((app) =>
          app.id === applicationId
            ? { ...app, status: status as ApplicationStatus }
            : app,
        ),
      );

      toast({
        title: "Success",
        description: `Application ${status} successfully`,
        variant: "success",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : `Failed to ${status} application`,
        variant: "destructive",
      });
    } finally {
      setUpdatingId(null);
    }
  };

  const getStatusColor = (status: ApplicationStatus) => {
    switch (status) {
      case ApplicationStatus.PENDING:
        return "tw-bg-yellow-900/30 tw-text-yellow-400 tw-ring-yellow-400/30";
      case ApplicationStatus.ACCEPTED:
        return "tw-bg-green-900/30 tw-text-green-400 tw-ring-green-400/30";
      case ApplicationStatus.REJECTED:
        return "tw-bg-red-900/30 tw-text-red-400 tw-ring-red-400/30";
      case ApplicationStatus.WITHDRAWN:
        return "tw-bg-gray-900/30 tw-text-gray-400 tw-ring-gray-400/30";
    }
  };

  const payment = campaign.deliverables.reduce(
    (sum, d) => sum + d.minimumPayment,
    0,
  );

  const giftedDeliverable = campaign.deliverables.find(
    (d) => d.type === DeliverableType.GIFTED_COLLABORATION,
  );


  const fileColumns: TableColumn<{
    url: string;
    originalName: string;
  }>[] = [
    {
      header: "FILE NAME",
      accessor: "originalName",
      className:
        "tw-px-3 tw-py-3 sm:tw-py-4 tw-text-left tw-text-xs sm:tw-text-sm tw-font-medium tw-text-aims-text-secondary tw-min-w-[200px]",
    },
  ];

  // Define columns for the Applications Table component
  const applicationColumns: TableColumn<Application>[] = [
    {
      header: "Athlete",
      className:
        "tw-py-3.5 tw-pl-4 tw-pr-3 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary tw-sm:tw-pl-6",
      cell: (application) => {
        const athleteProfile =
          typeof application.athleteId === "object"
            ? application.athleteId
            : null;
        const athleteName = athleteProfile?.userId?.name || "Unknown Athlete";
        const profilePicUrl =
          athleteProfile?.profilePicture?.url || "/no-profile-pic.jpg";
        return (
          <div className="tw-flex tw-items-center">
            {athleteProfile ? (
              <Link
                href={`/app/athlete/${athleteProfile._id}`}
                className="tw-flex tw-items-center"
              >
                <div className="tw-relative tw-h-10 tw-w-10 tw-rounded-full tw-overflow-hidden tw-flex-shrink-0">
                  <Image
                    src={profilePicUrl}
                    alt={athleteName}
                    fill
                    className="tw-object-cover tw-object-center"
                    sizes="40px"
                  />
                </div>
                <div className="tw-ml-4">
                  <div className="tw-font-medium tw-text-aims-text-primary">
                    {athleteName}
                  </div>
                </div>
              </Link>
            ) : (
              <div className="tw-ml-4">
                <div className="tw-font-medium tw-text-aims-text-secondary">
                  Athlete not found
                </div>
              </div>
            )}
          </div>
        );
      },
    },
    {
      header: "Type",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (application) => (
        <span className="tw-text-sm tw-text-aims-text-secondary">
          {application.initiatedBy === "brand" ? "Invitation" : "Application"}
        </span>
      ),
    },
    {
      header: "Status",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (application) => (
        <span
          className={`tw-inline-flex tw-items-center tw-rounded-md tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-ring-1 tw-ring-inset ${getStatusColor(application.status)}`}
        >
          {application.status}
        </span>
      ),
    },
    {
      header: "Applied Date",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (application) => (
        <span className="tw-text-sm tw-text-aims-text-secondary">
          {format(new Date(application.appliedAt), "MMM d, yyyy")}
        </span>
      ),
    },
    {
      header: "Message",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (application) => (
        <div className="tw-max-w-xs tw-truncate tw-text-sm tw-text-aims-text-secondary">
          {application.message || "No message provided"}
        </div>
      ),
    },
    {
      header: "Contract",
      className:
        "tw-px-3 tw-py-3.5 tw-text-left tw-text-sm tw-font-medium tw-text-aims-text-secondary",
      cell: (application) => {
        // Only render contract status if we have valid athlete data
        const hasValidAthleteData =
          application.athleteId &&
          (typeof application.athleteId === 'string' ||
           (typeof application.athleteId === 'object' &&
            application.athleteId.userId &&
            application.athleteId.userId.name));

        if (!hasValidAthleteData) {
          return (
            <div className="tw-text-sm tw-text-aims-text-secondary">
              No athlete data
            </div>
          );
        }

        // Create a type-safe application object for the component
        const typeSafeApplication = {
          id: application.id,
          campaignId: application.campaignId,
          status: application.status,
          athleteId: application.athleteId as string | {
            _id: string;
            userId: {
              _id: string;
              name: string;
            };
          }
        };

        return (
          <ApplicationContractStatus
            application={typeSafeApplication}
            campaignName={campaign?.name || "Campaign"}
            onContractGenerated={() => {
              toast({
                title: "Contract Generated",
                description: "Contract has been generated successfully.",
                variant: "success",
              });
            }}
          />
        );
      },
    },
  ];

  return (
    <div className="tw-p-4 sm:tw-p-6 lg:tw-p-8">
      <CampaignApplicationModal
        campaign={campaign}
        isOpen={isApplicationModalOpen}
        onClose={() => setIsApplicationModalOpen(false)}
        refetchCampaigns={() => {
          // Refetch campaign data
          void utils.campaign.getByCampaignId.invalidate({
            campaignId: id as string,
          });
        }}
        refetchApplications={() => {
          // Refetch applications data
          void utils.campaign.getAthleteApplications.invalidate();
        }}
      />

      {/* Campaign Header */}
      <div className="tw-flex tw-flex-col sm:tw-flex-row tw-gap-4 sm:tw-gap-6 lg:tw-gap-8 tw-items-start tw-mb-6 sm:tw-mb-8">
        <div className="tw-h-32 sm:tw-h-40 tw-aspect-square tw-relative tw-flex-shrink-0">
          <Image
            src={brand.logo.url || "/no-profile-pic.jpg"}
            alt="Campaign"
            fill
            className="tw-object-contain tw-rounded-lg"
            sizes="(max-width: 640px) 128px, 160px"
            priority
          />
        </div>
        <div className="tw-flex tw-flex-col tw-gap-2 tw-flex-1 tw-min-w-0">
          {/* Brand and Title */}
          <div className="tw-text-sm sm:tw-text-base tw-text-aims-text-primary">
            {brand.companyName}{" "}
            <a
              href={`/app/brand/${campaign.brandId}`}
              className="tw-text-aims-primary tw-text-xs sm:tw-text-sm tw-ml-2 tw-inline-flex tw-items-center tw-min-h-[44px]"
            >
              View brand profile
            </a>
          </div>
          <div className="tw-text-2xl sm:tw-text-3xl tw-font-bold tw-text-aims-text-primary tw-leading-tight">
            {campaign.name}
          </div>
          {/* Payment */}
          <div className="tw-text-3xl sm:tw-text-4xl lg:tw-text-5xl tw-font-bold tw-text-aims-text-primary tw-flex tw-flex-col sm:tw-flex-row sm:tw-items-center tw-gap-1 sm:tw-gap-2">
            <div className="tw-flex tw-items-center">
              <span className="tw-text-lg sm:tw-text-xl lg:tw-text-2xl">$</span>
              {payment.toFixed(2)}
            </div>
                          <span className="tw-text-xs sm:tw-text-sm tw-font-normal tw-text-aims-dark-6">
              {giftedDeliverable ? (
                <span>
                  + <GiftIcon className="tw-w-4 tw-h-4 tw-inline" />
                </span>
              ) : (
                ""
              )}
              </span>
          </div>
          {handleApplicationArea()}
        </div>
      </div>

      {/* Tabs - Only show Applications tab for brand view */}
      {brandView ? (
        <div className="tw-flex tw-gap-4 sm:tw-gap-8 tw-mb-4 sm:tw-mb-6 tw-border-b tw-border-gray-700 tw-overflow-x-auto tw-pb-2 sm:tw-pb-0">
          <button
            className={`tw-pb-2 tw-font-semibold tw-text-sm sm:tw-text-base tw-min-h-[44px] tw-flex tw-items-center tw-whitespace-nowrap tw-px-1 tw-transition-colors ${
              activeTab === "details"
                ? "tw-border-b-2 tw-border-aims-primary tw-text-aims-text-primary"
                : "tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
            }`}
            onClick={() => setActiveTab("details")}
          >
            Details
          </button>
          <button
            className={`tw-pb-2 tw-font-semibold tw-text-sm sm:tw-text-base tw-min-h-[44px] tw-flex tw-items-center tw-whitespace-nowrap tw-px-1 tw-transition-colors ${
              activeTab === "participant-management"
                ? "tw-border-b-2 tw-border-aims-primary tw-text-aims-text-primary"
                : "tw-text-aims-text-secondary hover:tw-text-aims-text-primary"
            }`}
            onClick={() => setActiveTab("participant-management")}
          >
            Participant Management
          </button>
        </div>
      ) : null}

      {/* Tab Content */}
      {(!brandView || activeTab === "details") && (
        <div className="tw-flex tw-flex-col lg:tw-flex-row tw-gap-4 lg:tw-gap-8">
          {/* Campaign Details */}
          <div className="tw-flex-1">
            <p className="tw-text-base sm:tw-text-lg tw-font-bold tw-mb-3 sm:tw-mb-4">Details</p>
        {/* Interests */}
        <p className="tw-text-sm tw-text-aims-dark-6 tw-mb-2">Campaign categories</p>
        <div className="tw-flex tw-flex-wrap tw-gap-2 tw-mb-4">
          {campaign.interests.map((interest: string, i: number) => (
            <span
              key={i}
              className="tw-border tw-border-gray-600 tw-bg-gray-700 tw-text-gray-200 tw-px-3 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium"
            >
              {interest}
            </span>
          ))}
        </div>
        {/* Participants */}

        {campaign.athletes && campaign.athletes.length > 0 && (
          <div className="tw-mt-4 sm:tw-mt-6">
            <p className="tw-text-sm tw-text-aims-dark-6 tw-mb-2">
              Campaign participants
            </p>
            <div className="tw-flex tw-items-center tw-flex-wrap tw-gap-1">
              {campaign.athletes.slice(0, 5).map((athlete, i: number) => (
                <Link
                  key={i}
                  href={`/app/athlete/${athlete._id}`}
                  className="tw-flex tw-items-center tw-gap-1"
                >
                  <Image
                    src={athlete.profilePicture.url || "/no-profile-pic.jpg"}
                    alt="athlete"
                    className="tw-w-8 tw-h-8 tw-rounded-full tw-border-2 tw-border-white"
                    width={144}
                    height={144}
                    sizes="32px"
                  />
                </Link>
              ))}
              {campaign.athletes.length > 5 && (
                <span className="tw-ml-1 tw-bg-[#232A3A] tw-text-aims-text-primary tw-px-2 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium">
                  +{campaign.athletes.length - 5}
                </span>
              )}
            </div>
          </div>
        )}
        {/* Description */}
        <div className="tw-mt-4 sm:tw-mt-6">
          <div className="tw-font-semibold tw-text-aims-text-primary tw-text-base sm:tw-text-lg tw-mb-3 sm:tw-mb-4">
            About the campaign
          </div>
          <p className="tw-text-sm tw-text-aims-dark-6 tw-mb-2">Description</p>
          <div className="tw-text-sm tw-text-aims-text-primary tw-leading-relaxed">
            {campaign.description}
          </div>
        </div>

        {/* Files */}
        {campaign.files && campaign.files.length > 0 && (
          <div className="tw-mt-6">
            <p className="tw-text-sm tw-text-aims-dark-6 tw-mb-2">Files</p>
            <div className="tw-overflow-x-auto tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
              <div className="tw-min-w-full">
                <Table
                  columns={fileColumns}
                  data={campaign.files}
                  rowKey={(row, idx) => row.originalName + idx}
                  actions={(row) => (
                    <Button
                      variant="outline"
                      className="tw-text-aims-text-primary tw-text-xs tw-h-8 tw-px-3 tw-whitespace-nowrap"
                      onClick={() => {
                        window.open(row.url, "_blank");
                      }}
                    >
                      View
                    </Button>
                  )}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Deliverables */}
      <div className="tw-bg-aims-dark-3 tw-rounded-xl tw-p-4 sm:tw-p-6 lg:tw-p-8 tw-flex-1 lg:tw-max-w-lg">
        <div className="tw-text-base sm:tw-text-lg tw-font-bold tw-text-aims-text-primary tw-mb-3 sm:tw-mb-4">
          Deliverables
        </div>
        <div className="tw-space-y-2 sm:tw-space-y-3">
          {campaign.deliverables.map((d: Deliverable, i: number) => (
            <details
              key={i}
              className="tw-bg-aims-dark-2 tw-rounded-lg tw-overflow-hidden tw-group"
            >
              <summary
                className="
                  tw-cursor-pointer tw-select-none
                  tw-px-4 sm:tw-px-6 tw-py-4 sm:tw-py-4
                  tw-flex tw-items-center tw-justify-between
                  tw-font-medium tw-text-sm sm:tw-text-base tw-text-aims-text-primary
                  tw-bg-aims-dark-2
                  tw-border-b tw-border-aims-dark-3
                  hover:tw-bg-aims-dark-4
                  focus:tw-outline-none
                  tw-min-h-[44px]
                  active:tw-bg-aims-dark-4
                  tw-transition-colors
                "
              >
                <div className="tw-flex tw-items-center tw-gap-2">
                <span className="tw-pr-2">{d.name}</span>
                {(() => {
                  if (brandView && user?.userType === "brand") {
                    // Brand view: Show status badges for all submissions for this deliverable
                    const submissions = getDeliverableSubmissions(d.id);
                    if (submissions.length > 0) {
                      // Show badges for each unique status
                      const statusCounts = submissions.reduce((acc, sub) => {
                        acc[sub.status] = (acc[sub.status] || 0) + 1;
                        return acc;
                      }, {} as Record<string, number>);

                      return (
                        <div className="tw-flex tw-items-center tw-gap-1">
                          {Object.entries(statusCounts).map(([status, count]) => (
                            <div key={status} className="tw-flex tw-items-center tw-gap-1">
                              <SubmissionStatusBadge status={status as any} />
                              {count > 1 && (
                                <span className="tw-text-xs tw-text-aims-text-secondary tw-bg-aims-dark-4 tw-rounded-full tw-w-5 tw-h-5 tw-flex tw-items-center tw-justify-center">
                                  {count}
                                </span>
                              )}
                            </div>
                          ))}
                        </div>
                      );
                    }
                  } else {
                    // Athlete view: Show status badge for their submission
                    const submission = getDeliverableSubmission(d.id);
                    if (submission) {
                      return <SubmissionStatusBadge status={submission.status} />;
                    } else if (hasActiveContract) {
                      // Show AWAITING_SUBMISSION if athlete has active contract but no submission
                      return <SubmissionStatusBadge status={DeliverableSubmissionStatus.AWAITING_SUBMISSION} />;
                    }
                    return null;
                  }
                  return null;
                })()}
                </div>
                <ChevronDownIcon
                  className="
                    tw-w-5 tw-h-5 tw-transition-transform tw-duration-200
                    group-open:tw-rotate-180
                    tw-text-aims-text-secondary
                    tw-flex-shrink-0
                  "
                />
              </summary>
              <div className="tw-px-4 sm:tw-px-6 tw-py-4 tw-bg-aims-dark-2">
                <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-3 tw-gap-3 sm:tw-gap-4 tw-mb-4">
                  <div>
                    <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-1">
                      Minimum payment
                    </span>
                    <div className="tw-font-bold tw-text-sm sm:tw-text-base tw-text-aims-text-primary">
                      ${d.minimumPayment}
                    </div>
                  </div>
                  <div>
                    <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-1">
                      Days to complete
                    </span>
                    <div className="tw-font-bold tw-text-sm sm:tw-text-base tw-text-aims-text-primary">
                      {d.daysToComplete} days
                    </div>
                  </div>
                  <div>
                    <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-1">Type</span>
                    <div className="tw-font-bold tw-text-sm sm:tw-text-base tw-text-aims-text-primary">
                      {getDeliverableTypeLabel(d.type)}
                    </div>
                  </div>
                </div>
                {d.content && d.content.length > 0 && (
                  <div className="tw-mb-3">
                    <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-2">
                      Content
                    </span>
                    <div className="tw-flex tw-flex-wrap tw-gap-2">
                      {d.content.map((content: string, idx: number) => (
                        <span
                          key={idx}
                          className="tw-bg-aims-dark-4 tw-text-aims-text-primary tw-px-3 tw-py-1 tw-rounded-full tw-text-xs tw-font-medium"
                        >
                          {content}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                {/* Additional fields in a responsive grid */}
                <div className="tw-grid tw-grid-cols-1 sm:tw-grid-cols-2 tw-gap-3 tw-mb-3">
                  {d.location && (
                    <div>
                      <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-1">
                        Location
                      </span>
                      <div className="tw-font-bold tw-text-sm sm:tw-text-base tw-text-aims-text-primary">
                        {d.location}
                      </div>
                    </div>
                  )}
                  {d.date && (
                    <div>
                      <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-1">Date</span>
                      <div className="tw-font-bold tw-text-sm sm:tw-text-base tw-text-aims-text-primary">
                        {d.date}
                      </div>
                    </div>
                  )}
                  {d.time && (
                    <div>
                      <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-1">Time</span>
                      <div className="tw-font-bold tw-text-sm sm:tw-text-base tw-text-aims-text-primary">
                        {d.time}
                      </div>
                    </div>
                  )}
                  {d.productName && (
                    <div>
                      <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-1">
                        Product
                      </span>
                      <div className="tw-font-bold tw-text-sm sm:tw-text-base tw-text-aims-text-primary">
                        {d.productName}
                        {d.productPrice && (
                          <span className="tw-text-aims-dark-6 tw-text-xs sm:tw-text-sm tw-block sm:tw-inline sm:tw-ml-2">
                            (${d.productPrice})
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <span className="tw-text-aims-dark-6 tw-text-xs tw-block tw-mb-1">
                    Description
                  </span>
                  <div className="tw-text-sm tw-text-aims-text-secondary tw-leading-relaxed">
                    {d.description}
                  </div>
                </div>

                {/* Submission Section for Athletes */}
                {!brandView && user?.userType === "athlete" && hasActiveContract && (
                  <div className="tw-mt-4 tw-pt-4 tw-border-t tw-border-aims-dark-3">
                    {isSubmissionsLoading ? (
                      <div className="tw-text-xs tw-text-aims-text-secondary">
                        Loading submission status...
                      </div>
                    ) : (() => {
                      const submission = getDeliverableSubmission(d.id);

                      if (submission) {
                        const canResubmit = submission.status === DeliverableSubmissionStatus.REJECTED ||
                                          submission.status === DeliverableSubmissionStatus.NEEDS_REVISION;

                        return (
                          <div className="tw-space-y-3">
                            <div className="tw-text-xs tw-text-aims-text-secondary">
                              Submitted on {format(new Date(submission.submittedAt), "MMM d, yyyy 'at' h:mm a")}
                            </div>
                            {submission.feedback && (
                              <div className="tw-bg-aims-dark-4 tw-rounded-lg tw-p-3">
                                <span className="tw-text-xs tw-font-medium tw-text-aims-text-primary tw-block tw-mb-1">
                                  Feedback
                                </span>
                                <div className="tw-text-xs tw-text-aims-text-secondary">
                                  {submission.feedback}
                                </div>
                              </div>
                            )}
                            {canResubmit && (
                              <Button
                                onClick={() => handleSubmitDeliverable(d.id, d.name, submission)}
                                className="tw-w-full sm:tw-w-auto tw-h-12 sm:tw-h-10 tw-bg-orange-600 hover:tw-bg-orange-700 tw-text-white tw-font-medium tw-rounded-lg tw-px-4 tw-py-2 tw-flex tw-items-center tw-justify-center tw-gap-2 tw-transition-colors"
                              >
                                <DocumentArrowUpIcon className="tw-w-4 tw-h-4" />
                                {submission.status === DeliverableSubmissionStatus.REJECTED ? "Resubmit" : "Submit Revision"}
                              </Button>
                            )}
                          </div>
                        );
                      } else {
                        // No submission exists, show submit button
                        return (
                          <Button
                            onClick={() => handleSubmitDeliverable(d.id, d.name)}
                            className="tw-w-full sm:tw-w-auto tw-h-12 sm:tw-h-10 tw-bg-green-600 hover:tw-bg-green-700 tw-text-white tw-font-medium tw-rounded-lg tw-px-4 tw-py-2 tw-flex tw-items-center tw-justify-center tw-gap-2 tw-transition-colors"
                          >
                            <DocumentArrowUpIcon className="tw-w-4 tw-h-4" />
                            Submit Deliverable
                          </Button>
                        );
                      }
                    })()}
                  </div>
                )}

                {/* Message for athletes without active contracts */}
                {!brandView && user?.userType === "athlete" && !hasActiveContract && (
                  <div className="tw-mt-4 tw-pt-4 tw-border-t tw-border-aims-dark-3">
                    <div className="tw-text-xs tw-text-aims-text-secondary tw-italic">
                      You need an active contract to submit deliverables
                    </div>
                  </div>
                )}

                {/* Brand view - Show submissions for this deliverable */}
                {brandView && user?.userType === "brand" && (
                  <div className="tw-mt-4 tw-pt-4 tw-border-t tw-border-aims-dark-3">
                    {(() => {
                      const submissions = getDeliverableSubmissions(d.id);

                      if (submissions.length === 0) {
                        return (
                          <div className="tw-text-xs tw-text-aims-text-secondary tw-italic">
                            No submissions yet
                          </div>
                        );
                      }

                      return (
                        <div className="tw-space-y-3">
                          <div className="tw-flex tw-items-center tw-justify-between">
                            <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary">
                              Submissions ({submissions.length})
                            </span>
                          </div>
                          <div className="tw-space-y-2">
                            {submissions.map((submission) => (
                              <div
                                key={submission.id}
                                className="tw-flex tw-items-center tw-justify-between tw-p-3 tw-bg-aims-dark-4 tw-rounded-lg"
                              >
                                <div className="tw-flex-1">
                                  <div className="tw-flex tw-items-center tw-gap-2 tw-mb-1">
                                    <span className="tw-text-sm tw-font-medium tw-text-aims-text-primary">
                                      {submission.athlete?.name || "Unknown Athlete"}
                                    </span>
                                    <SubmissionStatusBadge status={submission.status} />
                                  </div>
                                  <div className="tw-text-xs tw-text-aims-text-secondary">
                                    Submitted {format(new Date(submission.submittedAt), "MMM d, yyyy")}
                                  </div>
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => handleReviewSubmission(submission)}
                                  className="tw-ml-3 tw-text-aims-text-primary"
                                >
                                  Review
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>
            </details>
          ))}
            </div>
          </div>
        </div>
      )}

      {/* Applications Tab Content */}
      {brandView && activeTab === "participant-management" && (
        <div className="tw-space-y-6">
          <h2 className="tw-text-xl tw-font-semibold tw-text-aims-text-primary">
            Participant Management
          </h2>

          {/* Contract Overview */}
          <CampaignContractOverview campaignId={id as string} />

          <div className="tw-mt-8 tw-flow-root">
            <div className="tw--mx-4 tw--my-2 tw-overflow-x-auto tw-sm:tw--mx-6 lg:tw--mx-8">
              <div className="tw-inline-block tw-min-w-full tw-py-2 tw-align-middle tw-sm:tw-px-6 lg:tw-px-8">
                <div className="tw-overflow-hidden tw-rounded-lg tw-ring-1 tw-ring-aims-dark-3">
                  <Table
                    columns={applicationColumns}
                    data={applications}
                    rowKey={(row) => row.id}
                    actions={(application) => {
                      if (application.status !== ApplicationStatus.PENDING) {
                        return null;
                      }

                      // For brand-initiated invitations, show "Invitation Sent" status
                      if (application.initiatedBy === "brand") {
                        return (
                          <div className="tw-flex tw-justify-end">
                            <span className="tw-text-sm tw-text-aims-text-secondary tw-italic">
                              Invitation Sent
                            </span>
                          </div>
                        );
                      }

                      // For athlete-initiated applications, show Accept/Reject buttons
                      return (
                        <div className="tw-flex tw-justify-end tw-gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="tw-text-green-400/70 hover:tw-text-green-400"
                            onClick={() =>
                              handleUpdateStatus(
                                application.id,
                                ApplicationStatus.ACCEPTED,
                              )
                            }
                            disabled={updatingId === application.id}
                          >
                            {updatingId === application.id ? (
                              <LoadingSpinner />
                            ) : (
                              <>
                                <CheckIcon className="tw-h-4 tw-w-4 tw-mr-1" />
                                Accept
                              </>
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="tw-text-red-400/70 hover:tw-text-red-400"
                            onClick={() =>
                              handleUpdateStatus(
                                application.id,
                                ApplicationStatus.REJECTED,
                              )
                            }
                            disabled={updatingId === application.id}
                          >
                            {updatingId === application.id ? (
                              <LoadingSpinner />
                            ) : (
                              <>
                                <XMarkIcon className="tw-h-4 tw-w-4 tw-mr-1" />
                                Reject
                              </>
                            )}
                          </Button>
                        </div>
                      );
                    }}
                    emptyState={
                      <div className="tw-flex tw-flex-col tw-items-center tw-gap-2">
                        <p className="tw-font-medium tw-text-aims-text-primary">
                          No participants found
                        </p>
                        <p className="tw-text-aims-text-secondary">
                          No athletes have applied or been invited to this campaign yet
                        </p>
                        <Button
                          asChild
                          className="tw-flex tw-items-center tw-gap-2"
                        >
                          <Link href={`/app/brand/athletes`}>
                          <PlusIcon className="tw-w-5 tw-h-5" />
                          Invite Athletes
                          </Link>
                        </Button>
                      </div>
                    }
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Deliverable Submission Modal */}
      {selectedDeliverable && (
        <DeliverableSubmissionModal
          isOpen={isSubmissionModalOpen}
          onClose={() => {
            setIsSubmissionModalOpen(false);
            setSelectedDeliverable(null);
            setSelectedSubmissionForResubmit(null);
          }}
          campaignId={id as string}
          deliverableId={selectedDeliverable.id}
          deliverableName={selectedDeliverable.name}
          onSubmissionComplete={handleSubmissionComplete}
          existingSubmission={selectedSubmissionForResubmit || undefined}
        />
      )}

      {/* Deliverable Review Modal */}
      {selectedSubmission && (
        <DeliverableReviewModal
          isOpen={isReviewModalOpen}
          onClose={() => {
            setIsReviewModalOpen(false);
            setSelectedSubmission(null);
          }}
          submission={selectedSubmission}
          onReviewComplete={handleReviewComplete}
        />
      )}
    </div>
  );
}
